package service

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service/logic"
)

// PushOrderNotification 推送订单相关通知
// 根据订单状态变化向相关用户推送通知
func (s *Service) PushOrderNotification(req *define.PushOrderRequest) error {
	return logic.PushOrderNotification(s.ctx, req)
}

// PushMessageNotification 推送消息通知
// 向用户推送新消息通知
func (s *Service) PushMessageNotification(req *define.PushMessageRequest) error {
	return logic.PushMessageNotification(s.ctx, req)
}
