package enums

// PushActionType 推送动作类型
type PushActionType int

const (
	PushActionViewOrderDetail PushActionType = 1 // 查看订单详情
	PushActionViewMessage     PushActionType = 2 // 查看消息
	PushActionViewPost        PushActionType = 3 // 查看帖子
	PushActionViewProfile     PushActionType = 4 // 查看个人资料
)

// String 返回推送动作类型的字符串表示
func (t PushActionType) String() string {
	switch t {
	case PushActionViewOrderDetail:
		return "view_order_detail"
	case PushActionViewMessage:
		return "view_message"
	case PushActionViewPost:
		return "view_post"
	case PushActionViewProfile:
		return "view_profile"
	default:
		return "unknown_action"
	}
}

// IsValid 检查推送动作类型是否有效
func (t PushActionType) IsValid() bool {
	switch t {
	case PushActionViewOrderDetail, PushActionViewMessage, PushActionViewPost, PushActionViewProfile:
		return true
	default:
		return false
	}
}

// Int32 返回推送动作类型的int32值
func (t PushActionType) Int32() int32 {
	return int32(t)
}

// GetDescription 获取推送动作描述
func (t PushActionType) GetDescription() string {
	switch t {
	case PushActionViewOrderDetail:
		return "查看订单详情"
	case PushActionViewMessage:
		return "查看消息"
	case PushActionViewPost:
		return "查看帖子"
	case PushActionViewProfile:
		return "查看个人资料"
	default:
		return "未知动作"
	}
}

// PushDataType 推送数据类型
type PushDataType int

const (
	PushDataTypeOrder   PushDataType = 1 // 订单类型
	PushDataTypeMessage PushDataType = 2 // 消息类型
	PushDataTypePost    PushDataType = 3 // 帖子类型
	PushDataTypeUser    PushDataType = 4 // 用户类型
)

// String 返回推送数据类型的字符串表示
func (t PushDataType) String() string {
	switch t {
	case PushDataTypeOrder:
		return "order"
	case PushDataTypeMessage:
		return "message"
	case PushDataTypePost:
		return "post"
	case PushDataTypeUser:
		return "user"
	default:
		return "unknown"
	}
}

// IsValid 检查推送数据类型是否有效
func (t PushDataType) IsValid() bool {
	switch t {
	case PushDataTypeOrder, PushDataTypeMessage, PushDataTypePost, PushDataTypeUser:
		return true
	default:
		return false
	}
}

// Int32 返回推送数据类型的int32值
func (t PushDataType) Int32() int32 {
	return int32(t)
}

// GetDescription 获取推送数据类型描述
func (t PushDataType) GetDescription() string {
	switch t {
	case PushDataTypeOrder:
		return "订单"
	case PushDataTypeMessage:
		return "消息"
	case PushDataTypePost:
		return "帖子"
	case PushDataTypeUser:
		return "用户"
	default:
		return "未知类型"
	}
}

// MessageContentType 消息内容类型显示文本
type MessageContentType int

const (
	MessageContentTypeText  MessageContentType = 1 // 文本消息
	MessageContentTypeImage MessageContentType = 2 // 图片消息
	MessageContentTypePost  MessageContentType = 3 // 帖子分享
	MessageContentTypeOrder MessageContentType = 4 // 订单消息
)

// String 返回消息内容类型的显示文本
func (t MessageContentType) String() string {
	switch t {
	case MessageContentTypeText:
		return "" // 文本消息直接显示内容，不需要特殊标识
	case MessageContentTypeImage:
		return "[图片]"
	case MessageContentTypePost:
		return "[帖子分享]"
	case MessageContentTypeOrder:
		return "[订单消息]"
	default:
		return "[未知消息]"
	}
}

// IsValid 检查消息内容类型是否有效
func (t MessageContentType) IsValid() bool {
	switch t {
	case MessageContentTypeText, MessageContentTypeImage, MessageContentTypePost, MessageContentTypeOrder:
		return true
	default:
		return false
	}
}

// GetDisplayText 获取消息内容的显示文本
func (t MessageContentType) GetDisplayText() string {
	return t.String()
}
